/**
 * 服务注册模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RegistryController } from './registry.controller';
import { RegistryService } from './registry.service';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';
import { EnhancedServiceDiscoveryService } from './enhanced-service-discovery.service';
import { HealthCheckModule } from './health-check/health-check.module';
import { LoadBalancerModule } from './load-balancer/load-balancer.module';
import { ServiceCacheModule } from './cache/service-cache.module';
import { ServiceCacheController } from './cache/service-cache.controller';
import { EventBusModule } from '../shared/event-bus/event-bus.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ServiceEntity, ServiceInstanceEntity]),
    EventBusModule,
    HealthCheckModule,
    LoadBalancerModule,
    ServiceCacheModule,
  ],
  controllers: [RegistryController, ServiceCacheController],
  providers: [
    RegistryService,
    EnhancedServiceDiscoveryService,
  ],
  exports: [
    RegistryService,
    EnhancedServiceDiscoveryService,
  ],
})
export class RegistryModule {}
