# ================================
# DL引擎协作服务 .dockerignore
# 优化Docker构建性能
# ================================

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build
coverage

# 测试文件
test
tests
*.test.ts
*.spec.ts
jest.config.js

# 开发工具
.vscode
.idea
*.swp
*.swo
*~

# 版本控制
.git
.gitignore
.gitattributes

# 环境文件
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
logs
*.log

# 临时文件
tmp
temp
.tmp

# 文档
README.md
CHANGELOG.md
*.md

# 配置文件
.eslintrc.js
.prettierrc
tsconfig.json
nest-cli.json

# Docker相关
Dockerfile
.dockerignore
docker-compose*.yml

# 其他
.DS_Store
Thumbs.db
