import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, Not } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { EnhancedEventBusService, EventPriority } from '../shared/event-bus/enhanced-event-bus.service';
import { SERVICE_HEALTH_CHANGED, SERVICE_REGISTERED, SERVICE_DEREGISTERED } from '../shared/event-bus/events';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';
import { HealthCheckService } from './health-check/health-check.service';
import { LoadBalancerService } from './load-balancer/load-balancer.service';
import { ServiceCacheService } from './cache/service-cache.service';
import {
  HealthCheckType,
  HealthCheckConfig,
  HealthCheckStatus
} from './health-check/health-check.interface';
import {
  LoadBalancerContext,
  LoadBalancerAlgorithm
} from './load-balancer/load-balancer.interface';
import * as os from 'os';
import * as dns from 'dns';
import * as semver from 'semver';
import { promisify } from 'util';

/**
 * 服务健康状态
 */
export enum ServiceHealthStatus {
  UNKNOWN = 'unknown',
  STARTING = 'starting',
  HEALTHY = 'healthy',
  UNHEALTHY = 'unhealthy',
  DEGRADED = 'degraded',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
}

/**
 * 服务发现选项
 */
export interface ServiceDiscoveryOptions {
  /**
   * 服务名称
   */
  serviceName: string;

  /**
   * 服务版本
   */
  serviceVersion: string;

  /**
   * 服务实例ID
   */
  instanceId?: string;

  /**
   * 服务主机名
   */
  hostname?: string;

  /**
   * 服务IP地址
   */
  ipAddress?: string;

  /**
   * 服务端口
   */
  port: number;

  /**
   * 服务权重
   */
  weight?: number;

  /**
   * 服务区域/可用区
   */
  zone?: string;

  /**
   * 健康检查URL
   */
  healthCheckUrl?: string;

  /**
   * 健康检查类型
   */
  healthCheckType?: HealthCheckType;

  /**
   * 健康检查间隔（毫秒）
   */
  healthCheckInterval?: number;

  /**
   * 健康检查超时（毫秒）
   */
  healthCheckTimeout?: number;

  /**
   * 健康阈值（连续成功次数）
   */
  healthyThreshold?: number;

  /**
   * 不健康阈值（连续失败次数）
   */
  unhealthyThreshold?: number;

  /**
   * 健康检查配置
   */
  healthCheckConfig?: Partial<HealthCheckConfig>;

  /**
   * 服务元数据
   */
  metadata?: Record<string, any>;

  /**
   * 服务标签
   */
  tags?: string[];
}

/**
 * 增强的服务发现服务
 */
@Injectable()
export class EnhancedServiceDiscoveryService {
  private readonly logger = new Logger(EnhancedServiceDiscoveryService.name);
  private readonly lookupAsync = promisify(dns.lookup);
  private readonly serviceOptions: ServiceDiscoveryOptions;
  private readonly healthCheckInterval: number;
  private readonly serviceInstanceId: string;
  private readonly serviceCache = new Map<string, {
    service: ServiceEntity;
    instances: ServiceInstanceEntity[];
    lastUpdated: number;
  }>();
  private readonly cacheTTL: number;
  private readonly enableDnsDiscovery: boolean;
  private readonly dnsServicePrefix: string;
  private readonly dnsDomain: string;
  private isRegistered = false;

  constructor(
    @InjectRepository(ServiceEntity)
    private readonly serviceRepository: Repository<ServiceEntity>,
    @InjectRepository(ServiceInstanceEntity)
    private readonly serviceInstanceRepository: Repository<ServiceInstanceEntity>,
    private readonly configService: ConfigService,
    private readonly eventBus: EnhancedEventBusService,
    private readonly healthCheckService: HealthCheckService,
    private readonly loadBalancerService: LoadBalancerService,
    private readonly serviceCacheService: ServiceCacheService,
  ) {
    // 获取配置
    this.serviceOptions = {
      serviceName: this.configService.get<string>('SERVICE_NAME', 'unknown-service'),
      serviceVersion: this.configService.get<string>('SERVICE_VERSION', '1.0.0'),
      instanceId: this.configService.get<string>('SERVICE_INSTANCE_ID', `${os.hostname()}-${process.pid}`),
      hostname: os.hostname(),
      port: this.configService.get<number>('SERVICE_PORT', 3000),
      weight: this.configService.get<number>('SERVICE_WEIGHT', 100),
      zone: this.configService.get<string>('SERVICE_ZONE', ''),
      healthCheckUrl: this.configService.get<string>('HEALTH_CHECK_URL', '/health'),
      healthCheckType: this.configService.get<HealthCheckType>('HEALTH_CHECK_TYPE', HealthCheckType.HTTP),
      healthCheckInterval: this.configService.get<number>('HEALTH_CHECK_INTERVAL', 30000),
      healthCheckTimeout: this.configService.get<number>('HEALTH_CHECK_TIMEOUT', 5000),
      healthyThreshold: this.configService.get<number>('HEALTHY_THRESHOLD', 2),
      unhealthyThreshold: this.configService.get<number>('UNHEALTHY_THRESHOLD', 3),
      metadata: {
        startTime: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
      tags: this.configService.get<string>('SERVICE_TAGS', '').split(',').filter(Boolean),
    };

    this.serviceInstanceId = this.serviceOptions.instanceId;
    this.healthCheckInterval = this.serviceOptions.healthCheckInterval;
    this.cacheTTL = this.configService.get<number>('SERVICE_CACHE_TTL', 60000);
    this.enableDnsDiscovery = this.configService.get<boolean>('ENABLE_DNS_DISCOVERY', false);
    this.dnsServicePrefix = this.configService.get<string>('DNS_SERVICE_PREFIX', 'service-');
    this.dnsDomain = this.configService.get<string>('DNS_DOMAIN', 'service.local');

    // 自动注册服务
    if (this.configService.get<boolean>('AUTO_REGISTER_SERVICE', true)) {
      this.registerService().catch(error => {
        this.logger.error(`自动注册服务失败: ${error.message}`, error.stack);
      });
    }

    // 订阅服务健康状态变更事件
    this.eventBus.subscribe(SERVICE_HEALTH_CHANGED, async (event) => {
      try {
        const { serviceId, instanceId, status } = event.data;
        await this.updateServiceInstanceHealth(serviceId, instanceId, status);
      } catch (error) {
        this.logger.error(`处理服务健康状态变更事件失败: ${error.message}`, error.stack);
      }
    });
  }

  /**
   * 注册服务
   */
  async registerService(): Promise<ServiceInstanceEntity> {
    if (this.isRegistered) {
      this.logger.warn(`服务 ${this.serviceOptions.serviceName} 已经注册`);
      return this.getServiceInstance(this.serviceOptions.serviceName, this.serviceInstanceId);
    }

    try {
      // 获取IP地址
      const ipAddress = this.serviceOptions.ipAddress || await this.getLocalIpAddress();

      // 查找或创建服务
      let service = await this.serviceRepository.findOne({
        where: { name: this.serviceOptions.serviceName },
      });

      if (!service) {
        service = this.serviceRepository.create({
          name: this.serviceOptions.serviceName,
          version: this.serviceOptions.serviceVersion,
          description: `${this.serviceOptions.serviceName} service`,
          minCompatibleVersion: this.serviceOptions.serviceVersion,
          maxCompatibleVersion: null,
          type: 'service',
          tags: this.serviceOptions.tags,
          metadata: {
            registeredAt: new Date().toISOString(),
          },
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        service = await this.serviceRepository.save(service);
        this.logger.log(`已创建服务: ${service.name}`);
      }

      // 创建健康检查配置
      const healthCheckConfig: HealthCheckConfig = {
        type: this.serviceOptions.healthCheckType || HealthCheckType.HTTP,
        target: this.serviceOptions.healthCheckUrl || `http://${ipAddress}:${this.serviceOptions.port}/health`,
        interval: this.serviceOptions.healthCheckInterval || 30000,
        timeout: this.serviceOptions.healthCheckTimeout || 5000,
        healthyThreshold: this.serviceOptions.healthyThreshold || 2,
        unhealthyThreshold: this.serviceOptions.unhealthyThreshold || 3,
        enabled: true,
        options: this.serviceOptions.healthCheckConfig?.options || {},
      };

      // 查找或创建服务实例
      let serviceInstance = await this.serviceInstanceRepository.findOne({
        where: {
          serviceId: service.id,
          instanceId: this.serviceInstanceId,
        },
      });

      if (!serviceInstance) {
        serviceInstance = this.serviceInstanceRepository.create({
          serviceId: service.id,
          instanceId: this.serviceInstanceId,
          host: this.serviceOptions.hostname,
          ipAddress,
          port: this.serviceOptions.port,
          healthCheckUrl: this.serviceOptions.healthCheckUrl,
          healthCheckConfig,
          weight: this.serviceOptions.weight || 100,
          zone: this.serviceOptions.zone || '',
          status: ServiceHealthStatus.STARTING,
          metadata: this.serviceOptions.metadata,
          tags: this.serviceOptions.tags,
          lastHeartbeat: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        serviceInstance = await this.serviceInstanceRepository.save(serviceInstance);
        this.logger.log(`已创建服务实例: ${serviceInstance.instanceId}`);
      } else {
        // 更新服务实例
        serviceInstance.host = this.serviceOptions.hostname;
        serviceInstance.ipAddress = ipAddress;
        serviceInstance.port = this.serviceOptions.port;
        serviceInstance.healthCheckUrl = this.serviceOptions.healthCheckUrl;
        serviceInstance.healthCheckConfig = healthCheckConfig;
        serviceInstance.weight = this.serviceOptions.weight || 100;
        serviceInstance.zone = this.serviceOptions.zone || '';
        serviceInstance.status = ServiceHealthStatus.STARTING;
        serviceInstance.metadata = this.serviceOptions.metadata;
        serviceInstance.tags = this.serviceOptions.tags;
        serviceInstance.lastHeartbeat = new Date();
        serviceInstance.updatedAt = new Date();

        serviceInstance = await this.serviceInstanceRepository.save(serviceInstance);
        this.logger.log(`已更新服务实例: ${serviceInstance.instanceId}`);
      }

      // 标记为已注册
      this.isRegistered = true;

      // 发布服务注册事件
      await this.eventBus.publish(
        SERVICE_REGISTERED,
        {
          serviceId: service.id,
          serviceName: service.name,
          instanceId: serviceInstance.instanceId,
          ipAddress: serviceInstance.ipAddress,
          port: serviceInstance.port,
          weight: serviceInstance.weight,
          zone: serviceInstance.zone,
        },
        {
          priority: EventPriority.HIGH,
        }
      );

      // 执行健康检查
      this.scheduleHealthCheck(serviceInstance);

      return serviceInstance;
    } catch (error) {
      this.logger.error(`注册服务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 注销服务
   */
  async deregisterService(): Promise<boolean> {
    if (!this.isRegistered) {
      this.logger.warn(`服务 ${this.serviceOptions.serviceName} 未注册`);
      return false;
    }

    try {
      // 查找服务
      const service = await this.serviceRepository.findOne({
        where: { name: this.serviceOptions.serviceName },
      });

      if (!service) {
        this.logger.warn(`服务 ${this.serviceOptions.serviceName} 不存在`);
        return false;
      }

      // 查找服务实例
      const serviceInstance = await this.serviceInstanceRepository.findOne({
        where: {
          serviceId: service.id,
          instanceId: this.serviceInstanceId,
        },
      });

      if (!serviceInstance) {
        this.logger.warn(`服务实例 ${this.serviceInstanceId} 不存在`);
        return false;
      }

      // 更新服务实例状态
      serviceInstance.status = ServiceHealthStatus.STOPPED;
      serviceInstance.updatedAt = new Date();
      await this.serviceInstanceRepository.save(serviceInstance);

      // 发布服务注销事件
      await this.eventBus.publish(
        SERVICE_DEREGISTERED,
        {
          serviceId: service.id,
          serviceName: service.name,
          instanceId: serviceInstance.instanceId,
        },
        {
          priority: EventPriority.HIGH,
        }
      );

      // 标记为未注册
      this.isRegistered = false;

      this.logger.log(`已注销服务实例: ${serviceInstance.instanceId}`);

      return true;
    } catch (error) {
      this.logger.error(`注销服务失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新服务实例健康状态
   * @param serviceId 服务ID
   * @param instanceId 实例ID
   * @param status 健康状态
   */
  async updateServiceInstanceHealth(
    serviceId: string,
    instanceId: string,
    status: ServiceHealthStatus,
  ): Promise<boolean> {
    try {
      // 查找服务实例
      const serviceInstance = await this.serviceInstanceRepository.findOne({
        where: {
          serviceId,
          instanceId,
        },
      });

      if (!serviceInstance) {
        this.logger.warn(`服务实例 ${instanceId} 不存在`);
        return false;
      }

      // 更新服务实例状态
      serviceInstance.status = status;
      serviceInstance.lastHeartbeat = new Date();
      serviceInstance.updatedAt = new Date();
      await this.serviceInstanceRepository.save(serviceInstance);

      // 清除缓存
      this.clearServiceCache(serviceId);

      this.logger.debug(`已更新服务实例健康状态: ${instanceId} => ${status}`);

      return true;
    } catch (error) {
      this.logger.error(`更新服务实例健康状态失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 发送心跳
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async sendHeartbeat(): Promise<void> {
    if (!this.isRegistered) {
      return;
    }

    try {
      // 查找服务
      const service = await this.serviceRepository.findOne({
        where: { name: this.serviceOptions.serviceName },
      });

      if (!service) {
        this.logger.warn(`服务 ${this.serviceOptions.serviceName} 不存在`);
        return;
      }

      // 查找服务实例
      const serviceInstance = await this.serviceInstanceRepository.findOne({
        where: {
          serviceId: service.id,
          instanceId: this.serviceInstanceId,
        },
      });

      if (!serviceInstance) {
        this.logger.warn(`服务实例 ${this.serviceInstanceId} 不存在`);
        return;
      }

      // 更新心跳时间
      serviceInstance.lastHeartbeat = new Date();
      serviceInstance.updatedAt = new Date();
      await this.serviceInstanceRepository.save(serviceInstance);

      this.logger.debug(`已发送心跳: ${this.serviceInstanceId}`);
    } catch (error) {
      this.logger.error(`发送心跳失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 检查不健康的服务实例
   */
  @Cron(CronExpression.EVERY_MINUTE)
  async checkUnhealthyInstances(): Promise<void> {
    try {
      // 查找超过心跳时间的服务实例
      const threshold = new Date();
      threshold.setTime(threshold.getTime() - this.healthCheckInterval * 2); // 2倍心跳间隔

      const unhealthyInstances = await this.serviceInstanceRepository.find({
        where: {
          lastHeartbeat: LessThan(threshold),
          status: Not(ServiceHealthStatus.STOPPED),
        },
      });

      for (const instance of unhealthyInstances) {
        // 更新为不健康状态
        instance.status = ServiceHealthStatus.UNHEALTHY;
        instance.updatedAt = new Date();
        await this.serviceInstanceRepository.save(instance);

        // 发布服务健康状态变更事件
        await this.eventBus.publish(
          SERVICE_HEALTH_CHANGED,
          {
            serviceId: instance.serviceId,
            instanceId: instance.instanceId,
            status: ServiceHealthStatus.UNHEALTHY,
          },
          {
            priority: EventPriority.HIGH,
          }
        );

        // 清除缓存
        this.clearServiceCache(instance.serviceId);

        this.logger.warn(`服务实例 ${instance.instanceId} 已标记为不健康`);
      }
    } catch (error) {
      this.logger.error(`检查不健康服务实例失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 获取服务实例
   * @param serviceName 服务名称
   * @param instanceId 实例ID
   */
  async getServiceInstance(
    serviceName: string,
    instanceId: string,
  ): Promise<ServiceInstanceEntity | null> {
    try {
      // 查找服务
      const service = await this.serviceRepository.findOne({
        where: { name: serviceName },
      });

      if (!service) {
        return null;
      }

      // 查找服务实例
      return await this.serviceInstanceRepository.findOne({
        where: {
          serviceId: service.id,
          instanceId,
        },
      });
    } catch (error) {
      this.logger.error(`获取服务实例失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 获取服务实例列表
   * @param serviceName 服务名称
   * @param onlyHealthy 是否只返回健康的实例
   * @param version 服务版本要求
   * @param zone 区域要求
   */
  async getServiceInstances(
    serviceName: string,
    onlyHealthy: boolean = true,
    version?: string,
    zone?: string,
  ): Promise<ServiceInstanceEntity[]> {
    try {
      // 构建缓存键
      const cacheKey = `instances:${serviceName}:${onlyHealthy}:${version || ''}:${zone || ''}`;

      // 使用服务缓存服务获取实例列表
      return await this.serviceCacheService.get<ServiceInstanceEntity[]>(
        cacheKey,
        async () => {
          // 查找服务
          const service = await this.serviceRepository.findOne({
            where: { name: serviceName },
          });

          if (!service) {
            return [];
          }

          // 检查版本兼容性
          if (version && !this.isVersionCompatible(service, version)) {
            this.logger.warn(`服务 ${serviceName} 版本 ${service.version} 与请求版本 ${version} 不兼容`);
            return [];
          }

          // 查找服务实例
          const query: any = {
            serviceId: service.id,
          };

          if (onlyHealthy) {
            query.status = ServiceHealthStatus.HEALTHY;
          }

          if (zone) {
            query.zone = zone;
          }

          return await this.serviceInstanceRepository.find({
            where: query,
          });
        }
      );
    } catch (error) {
      this.logger.error(`获取服务实例列表失败: ${error.message}`, error.stack);

      // 如果有旧缓存，则使用旧缓存
      const cacheKey = `${serviceName}:${onlyHealthy}:${version || ''}:${zone || ''}`;
      if (this.serviceCache.has(cacheKey)) {
        return this.serviceCache.get(cacheKey).instances;
      }

      return [];
    }
  }

  /**
   * 使用负载均衡选择服务实例
   * @param serviceName 服务名称
   * @param context 负载均衡上下文
   * @param onlyHealthy 是否只返回健康的实例
   * @param version 服务版本要求
   */
  async selectServiceInstance(
    serviceName: string,
    context?: Partial<LoadBalancerContext>,
    onlyHealthy: boolean = true,
    version?: string,
  ): Promise<ServiceInstanceEntity | null> {
    try {
      // 获取服务实例列表
      const instances = await this.getServiceInstances(
        serviceName,
        onlyHealthy,
        version,
        context?.clientZone,
      );

      if (!instances || instances.length === 0) {
        return null;
      }

      // 如果只有一个实例，直接返回
      if (instances.length === 1) {
        return instances[0];
      }

      // 使用负载均衡服务选择实例
      return this.loadBalancerService.selectInstance(
        serviceName,
        instances,
        {
          serviceName,
          ...context,
        },
      );
    } catch (error) {
      this.logger.error(`选择服务实例失败: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * 调度健康检查
   * @param instance 服务实例
   */
  private scheduleHealthCheck(instance: ServiceInstanceEntity): void {
    if (!instance.healthCheckConfig || !instance.healthCheckConfig.enabled) {
      return;
    }

    // 延迟执行第一次健康检查
    setTimeout(async () => {
      await this.performHealthCheck(instance);

      // 设置定期健康检查
      setInterval(async () => {
        await this.performHealthCheck(instance);
      }, instance.healthCheckConfig.interval);
    }, 5000); // 延迟5秒执行第一次健康检查
  }

  /**
   * 执行健康检查
   * @param instance 服务实例
   */
  private async performHealthCheck(instance: ServiceInstanceEntity): Promise<void> {
    try {
      if (!instance.healthCheckConfig || !instance.healthCheckConfig.enabled) {
        return;
      }

      // 执行健康检查
      const result = await this.healthCheckService.check(
        instance.instanceId,
        instance.healthCheckConfig,
      );

      // 更新服务实例健康状态
      let healthStatus: ServiceHealthStatus;

      switch (result.status) {
        case HealthCheckStatus.HEALTHY:
          healthStatus = ServiceHealthStatus.HEALTHY;
          break;
        case HealthCheckStatus.DEGRADED:
          healthStatus = ServiceHealthStatus.DEGRADED;
          break;
        case HealthCheckStatus.UNHEALTHY:
          healthStatus = ServiceHealthStatus.UNHEALTHY;
          break;
        default:
          healthStatus = ServiceHealthStatus.UNKNOWN;
      }

      // 更新服务实例健康状态
      await this.updateServiceInstanceHealth(
        instance.serviceId,
        instance.instanceId,
        healthStatus,
      );

      // 如果是最少响应时间负载均衡策略，则更新响应时间
      if (result.responseTime) {
        this.loadBalancerService.updateResponseTime(
          instance.serviceId,
          instance.instanceId,
          result.responseTime,
        );
      }
    } catch (error) {
      this.logger.error(`执行健康检查失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 检查版本兼容性
   * @param service 服务
   * @param version 版本
   */
  private isVersionCompatible(service: ServiceEntity, version: string): boolean {
    try {
      // 如果服务版本与请求版本相同，则兼容
      if (service.version === version) {
        return true;
      }

      // 如果未设置兼容性版本范围，则只兼容相同版本
      if (!service.minCompatibleVersion && !service.maxCompatibleVersion) {
        return service.version === version;
      }

      // 检查版本范围
      if (service.minCompatibleVersion && service.maxCompatibleVersion) {
        return semver.satisfies(
          version,
          `>=${service.minCompatibleVersion} <=${service.maxCompatibleVersion}`,
        );
      }

      // 只检查最小兼容版本
      if (service.minCompatibleVersion && !service.maxCompatibleVersion) {
        return semver.gte(version, service.minCompatibleVersion);
      }

      // 只检查最大兼容版本
      if (!service.minCompatibleVersion && service.maxCompatibleVersion) {
        return semver.lte(version, service.maxCompatibleVersion);
      }

      return false;
    } catch (error) {
      this.logger.error(`检查版本兼容性失败: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * 通过DNS发现服务
   * @param serviceName 服务名称
   */
  async discoverServiceByDns(serviceName: string): Promise<{
    host: string;
    port: number;
  }[]> {
    if (!this.enableDnsDiscovery) {
      return [];
    }

    try {
      // 构建DNS名称
      const dnsName = `${this.dnsServicePrefix}${serviceName}.${this.dnsDomain}`;

      // 解析DNS
      const { address } = await this.lookupAsync(dnsName);

      // 默认使用标准端口
      const defaultPort = serviceName === 'http' ? 80 : (serviceName === 'https' ? 443 : 3000);

      return [{
        host: address,
        port: defaultPort,
      }];
    } catch (error) {
      this.logger.error(`通过DNS发现服务失败: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * 获取本地IP地址
   */
  private async getLocalIpAddress(): Promise<string> {
    try {
      const interfaces = os.networkInterfaces();

      for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
          // 跳过内部和非IPv4地址
          if (iface.internal || iface.family !== 'IPv4') {
            continue;
          }

          return iface.address;
        }
      }

      // 如果没有找到合适的地址，则使用localhost
      return '127.0.0.1';
    } catch (error) {
      this.logger.error(`获取本地IP地址失败: ${error.message}`, error.stack);
      return '127.0.0.1';
    }
  }

  /**
   * 清除服务缓存
   * @param serviceId 服务ID
   */
  private clearServiceCache(serviceId: string): void {
    // 查找包含该服务ID的缓存项
    for (const [key, cached] of this.serviceCache.entries()) {
      if (cached.service.id === serviceId) {
        this.serviceCache.delete(key);
      }
    }
  }
}
