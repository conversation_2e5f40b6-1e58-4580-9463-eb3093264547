/**
 * 事件常量定义
 */

// 服务注册事件
export const SERVICE_REGISTERED = 'service.registered';
export const SERVICE_DEREGISTERED = 'service.deregistered';
export const SERVICE_HEALTH_CHANGED = 'service.health.changed';

// 服务发现事件
export const SERVICE_DISCOVERED = 'service.discovered';
export const SERVICE_LOST = 'service.lost';

// 负载均衡事件
export const LOAD_BALANCER_UPDATED = 'load.balancer.updated';

// 健康检查事件
export const HEALTH_CHECK_STARTED = 'health.check.started';
export const HEALTH_CHECK_COMPLETED = 'health.check.completed';
export const HEALTH_CHECK_FAILED = 'health.check.failed';

// 缓存事件
export const CACHE_UPDATED = 'cache.updated';
export const CACHE_INVALIDATED = 'cache.invalidated';

// 监控事件
export const METRICS_COLLECTED = 'metrics.collected';
export const ALERT_TRIGGERED = 'alert.triggered';
