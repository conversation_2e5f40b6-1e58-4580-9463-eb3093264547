# DL引擎协作服务 Docker 配置说明

## 概述

本文档说明了DL引擎协作服务的Docker配置，包括Dockerfile优化、环境变量配置和部署最佳实践。

## Dockerfile 特性

### 多阶段构建
- **构建阶段**: 使用完整的Node.js环境编译TypeScript
- **生产阶段**: 使用精简的Alpine镜像运行应用

### 安全优化
- 使用非root用户运行应用
- 限制容器权限
- 最小化攻击面

### 性能优化
- 分层缓存优化
- 生产依赖分离
- 内存和CPU限制

## 环境变量配置

### 必需变量
```bash
NODE_ENV=production          # 运行环境
PORT=3005                   # 服务端口
INSTANCE_ID=collaboration-1 # 实例标识
JWT_SECRET=your_secret      # JWT密钥
REDIS_HOST=redis           # Redis主机
SERVICE_REGISTRY_HOST=service-registry # 服务注册中心
```

### 协作服务特定配置
```bash
ENABLE_COMPRESSION=true     # 启用消息压缩
COMPRESSION_LEVEL=6         # 压缩级别(1-9)
MAX_BATCH_SIZE=50          # 最大批处理大小
MAX_BATCH_WAIT_TIME=50     # 批处理等待时间(ms)
```

### WebSocket配置
```bash
WS_HEARTBEAT_INTERVAL=30000 # 心跳间隔(ms)
WS_MAX_CONNECTIONS=1000     # 最大连接数
WS_MESSAGE_SIZE_LIMIT=1048576 # 消息大小限制(bytes)
```

## 构建和运行

### 本地构建
```bash
# 构建镜像
docker build -t dl-engine-collaboration:latest .

# 运行容器
docker run -d \
  --name collaboration-service \
  -p 3005:3005 \
  --env-file .env \
  dl-engine-collaboration:latest
```

### Docker Compose
```yaml
collaboration-service-1:
  build:
    context: ./server/collaboration-service
    dockerfile: Dockerfile
  container_name: dl-engine-collaboration-service-1
  restart: always
  environment:
    - NODE_ENV=production
    - PORT=3005
    - INSTANCE_ID=collaboration-1
    - REDIS_HOST=redis
    - SERVICE_REGISTRY_HOST=service-registry
  ports:
    - "3005:3005"
  depends_on:
    - redis
    - service-registry
  healthcheck:
    test: ["CMD", "wget", "-qO-", "http://localhost:3005/health"]
    interval: 10s
    timeout: 5s
    retries: 3
```

## 健康检查

### 端点说明
- `/health` - 完整健康检查，包含依赖服务状态
- `/ready` - 就绪检查，确认服务可以接受请求
- `/live` - 存活检查，确认服务进程正常

### 健康检查响应
```json
{
  "status": "ok",
  "timestamp": "2025-07-14T10:00:00.000Z",
  "uptime": 300000,
  "version": "0.1.0",
  "instance": "collaboration-1",
  "services": {
    "redis": "connected",
    "websocket": "active",
    "serviceRegistry": "connected"
  },
  "metrics": {
    "activeConnections": 25,
    "totalMessages": 1500,
    "memoryUsage": {
      "used": 52428800,
      "total": 134217728,
      "percentage": 39
    },
    "cpuUsage": 15
  }
}
```

## 监控和日志

### 日志配置
- 使用结构化JSON日志格式
- 支持日志级别控制
- 可选文件日志输出

### 监控指标
- 活跃WebSocket连接数
- 消息处理速度
- 内存和CPU使用率
- 错误率统计

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看容器日志
   docker logs collaboration-service
   
   # 检查环境变量
   docker exec collaboration-service env
   ```

2. **健康检查失败**
   ```bash
   # 手动健康检查
   curl http://localhost:3005/health
   
   # 检查依赖服务
   docker exec collaboration-service nc -z redis 6379
   ```

3. **WebSocket连接问题**
   ```bash
   # 检查端口监听
   docker exec collaboration-service netstat -tlnp
   
   # 测试WebSocket连接
   wscat -c ws://localhost:3005
   ```

### 性能调优

1. **内存优化**
   ```bash
   # 调整Node.js内存限制
   NODE_OPTIONS=--max-old-space-size=1024
   ```

2. **连接数优化**
   ```bash
   # 增加最大连接数
   WS_MAX_CONNECTIONS=2000
   ```

3. **压缩优化**
   ```bash
   # 调整压缩级别
   COMPRESSION_LEVEL=3  # 更快的压缩
   ```

## 安全建议

1. **网络安全**
   - 使用内部网络隔离
   - 限制外部访问端口
   - 启用TLS加密

2. **容器安全**
   - 定期更新基础镜像
   - 扫描安全漏洞
   - 使用最小权限原则

3. **数据安全**
   - 加密敏感配置
   - 定期轮换密钥
   - 审计访问日志

## 部署最佳实践

1. **多实例部署**
   - 使用负载均衡器
   - 配置会话亲和性
   - 实现优雅关闭

2. **资源管理**
   - 设置资源限制
   - 监控资源使用
   - 自动扩缩容

3. **备份和恢复**
   - 定期备份配置
   - 测试恢复流程
   - 文档化操作步骤
