/**
 * 服务发现服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventBusService } from '../shared/event-bus';
import { SERVICE_HEALTH_CHANGED } from '../shared/event-bus/events';
import { ServiceEntity } from './entities/service.entity';
import { ServiceInstanceEntity } from './entities/service-instance.entity';

@Injectable()
export class ServiceDiscoveryService {
  private readonly logger = new Logger(ServiceDiscoveryService.name);

  constructor(
    @InjectRepository(ServiceEntity)
    private readonly serviceRepository: Repository<ServiceEntity>,
    @InjectRepository(ServiceInstanceEntity)
    private readonly instanceRepository: Repository<ServiceInstanceEntity>,
    private readonly eventBusService: EventBusService,
  ) {}

  /**
   * 获取服务实例
   * @param serviceName 服务名称
   * @param onlyHealthy 是否只返回健康的实例
   */
  async getServiceInstances(serviceName: string, onlyHealthy = true): Promise<ServiceInstanceEntity[]> {
    const service = await this.serviceRepository.findOne({
      where: { name: serviceName },
      relations: ['instances'],
    });
    
    if (!service) {
      return [];
    }
    
    if (onlyHealthy) {
      return service.instances.filter(instance => instance.status === 'UP');
    }
    
    return service.instances;
  }

  /**
   * 获取服务实例（负载均衡）
   * @param serviceName 服务名称
   * @param algorithm 负载均衡算法，默认为随机
   */
  async getServiceInstance(
    serviceName: string,
    algorithm: 'random' | 'round-robin' | 'least-connections' = 'random',
  ): Promise<ServiceInstanceEntity | null> {
    const instances = await this.getServiceInstances(serviceName);
    
    if (instances.length === 0) {
      return null;
    }
    
    switch (algorithm) {
      case 'random':
        // 随机选择一个实例
        return instances[Math.floor(Math.random() * instances.length)];
        
      case 'round-robin':
        // 轮询选择一个实例
        // 这里简单实现，实际应该保存上次选择的索引
        const index = Math.floor(Date.now() / 1000) % instances.length;
        return instances[index];
        
      case 'least-connections':
        // 选择连接数最少的实例
        // 这里简单实现，实际应该跟踪每个实例的连接数
        return instances.reduce((prev, curr) => {
          return (prev.metadata?.connections || 0) < (curr.metadata?.connections || 0) ? prev : curr;
        });
        
      default:
        return instances[Math.floor(Math.random() * instances.length)];
    }
  }

  /**
   * 定时检查服务实例健康状态
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async checkServiceHealth() {
    this.logger.debug('检查服务实例健康状态');
    
    const instances = await this.instanceRepository.find();
    const now = new Date();
    
    for (const instance of instances) {
      // 检查上次心跳时间，如果超过60秒没有心跳，则标记为不健康
      const lastHeartbeat = new Date(instance.lastHeartbeat);
      const timeSinceLastHeartbeat = now.getTime() - lastHeartbeat.getTime();
      
      if (timeSinceLastHeartbeat > 60000 && instance.status === 'UP') {
        // 更新实例状态为不健康
        instance.status = 'DOWN';
        instance.lastUpdated = now;
        await this.instanceRepository.save(instance);
        
        // 发布服务健康状态变更事件
        await this.eventBusService.publish(SERVICE_HEALTH_CHANGED, {
          serviceId: instance.id,
          status: 'unhealthy',
          details: '服务实例心跳超时',
          timestamp: now,
        });
        
        this.logger.warn(`服务实例 ${instance.instanceId} (${instance.host}:${instance.port}) 心跳超时，标记为不健康`);
      }
    }
  }

  /**
   * 更新服务实例心跳
   * @param instanceId 实例ID
   */
  async updateHeartbeat(instanceId: string): Promise<boolean> {
    const instance = await this.instanceRepository.findOne({
      where: { instanceId },
    });
    
    if (!instance) {
      return false;
    }
    
    const now = new Date();
    const wasDown = instance.status === 'DOWN';
    
    // 更新心跳时间和状态
    instance.lastHeartbeat = now;
    instance.status = 'UP';
    instance.lastUpdated = now;
    await this.instanceRepository.save(instance);
    
    // 如果实例之前是不健康的，现在恢复了，发布服务健康状态变更事件
    if (wasDown) {
      await this.eventBusService.publish(SERVICE_HEALTH_CHANGED, {
        serviceId: instance.id,
        status: 'healthy',
        details: '服务实例恢复正常',
        timestamp: now,
      });
      
      this.logger.log(`服务实例 ${instance.instanceId} (${instance.host}:${instance.port}) 恢复正常`);
    }
    
    return true;
  }
}
