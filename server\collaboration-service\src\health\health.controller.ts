/**
 * 健康检查控制器
 * 提供服务健康状态检查
 */
import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

/**
 * 健康检查响应接口
 */
export interface HealthResponse {
  status: 'ok' | 'error';
  timestamp: string;
  uptime: number;
  version: string;
  instance: string;
  services: {
    redis: 'connected' | 'disconnected';
    websocket: 'active' | 'inactive';
    serviceRegistry: 'connected' | 'disconnected';
  };
  metrics: {
    activeConnections: number;
    totalMessages: number;
    memoryUsage: {
      used: number;
      total: number;
      percentage: number;
    };
    cpuUsage: number;
  };
}

/**
 * 健康检查控制器
 */
@ApiTags('健康检查')
@Controller()
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  /**
   * 健康检查端点
   * @returns 健康状态
   */
  @Get('health')
  @ApiOperation({ summary: '健康检查' })
  @ApiResponse({ 
    status: 200, 
    description: '服务健康',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['ok', 'error'] },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        instance: { type: 'string' },
        services: {
          type: 'object',
          properties: {
            redis: { type: 'string', enum: ['connected', 'disconnected'] },
            websocket: { type: 'string', enum: ['active', 'inactive'] },
            serviceRegistry: { type: 'string', enum: ['connected', 'disconnected'] }
          }
        },
        metrics: {
          type: 'object',
          properties: {
            activeConnections: { type: 'number' },
            totalMessages: { type: 'number' },
            memoryUsage: {
              type: 'object',
              properties: {
                used: { type: 'number' },
                total: { type: 'number' },
                percentage: { type: 'number' }
              }
            },
            cpuUsage: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async getHealth(): Promise<HealthResponse> {
    return this.healthService.getHealthStatus();
  }

  /**
   * 就绪检查端点
   * @returns 就绪状态
   */
  @Get('ready')
  @ApiOperation({ summary: '就绪检查' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async getReady(): Promise<{ status: string; ready: boolean }> {
    const isReady = await this.healthService.isReady();
    return {
      status: isReady ? 'ready' : 'not ready',
      ready: isReady
    };
  }

  /**
   * 存活检查端点
   * @returns 存活状态
   */
  @Get('live')
  @ApiOperation({ summary: '存活检查' })
  @ApiResponse({ status: 200, description: '服务存活' })
  async getLive(): Promise<{ status: string; alive: boolean }> {
    return {
      status: 'alive',
      alive: true
    };
  }
}
