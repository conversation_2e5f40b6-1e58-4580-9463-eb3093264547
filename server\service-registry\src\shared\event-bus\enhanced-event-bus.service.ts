/**
 * 增强型Event Bus服务
 */
import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from './event-bus.service';

/**
 * 事件优先级
 */
export enum EventPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

/**
 * 事件元数据
 */
export interface EventMetadata {
  priority: EventPriority;
  timestamp: Date;
  source: string;
  correlationId?: string;
}

/**
 * 增强型事件
 */
export interface EnhancedEvent {
  type: string;
  payload: any;
  metadata: EventMetadata;
}

@Injectable()
export class EnhancedEventBusService {
  private readonly logger = new Logger(EnhancedEventBusService.name);
  private readonly eventQueue: EnhancedEvent[] = [];
  private processing = false;

  constructor(private readonly eventBus: EventBusService) {}

  /**
   * 发布增强型事件
   */
  async emitEnhanced(
    type: string,
    payload: any,
    priority: EventPriority = EventPriority.NORMAL,
    source: string = 'unknown',
    correlationId?: string
  ): Promise<void> {
    const event: EnhancedEvent = {
      type,
      payload,
      metadata: {
        priority,
        timestamp: new Date(),
        source,
        correlationId
      }
    };

    this.eventQueue.push(event);
    this.eventQueue.sort((a, b) => b.metadata.priority - a.metadata.priority);

    if (!this.processing) {
      await this.processQueue();
    }
  }

  /**
   * 处理事件队列
   */
  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        try {
          await this.eventBus.emit(event.type, {
            ...event.payload,
            metadata: event.metadata
          });
        } catch (error) {
          this.logger.error(`处理事件失败: ${event.type}`, error);
        }
      }
    }

    this.processing = false;
  }

  /**
   * 订阅事件
   */
  on(event: string, listener: (...args: any[]) => void): void {
    this.eventBus.on(event, listener);
  }

  /**
   * 取消订阅
   */
  off(event: string, listener: (...args: any[]) => void): void {
    this.eventBus.off(event, listener);
  }
}
