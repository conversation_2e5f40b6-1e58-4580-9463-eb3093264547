#!/bin/bash

# ================================
# DL引擎协作服务启动脚本
# 生产环境优化启动
# ================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境变量
check_env() {
    log_info "检查环境变量..."
    
    # 必需的环境变量
    required_vars=(
        "NODE_ENV"
        "PORT"
        "REDIS_HOST"
        "SERVICE_REGISTRY_HOST"
        "JWT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "环境变量 $var 未设置"
            exit 1
        fi
    done
    
    log_success "环境变量检查完成"
}

# 等待依赖服务
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service_name 服务启动 ($host:$port)..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            log_success "$service_name 服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name 服务连接超时"
    return 1
}

# 等待依赖服务启动
wait_for_dependencies() {
    log_info "等待依赖服务启动..."
    
    # 等待Redis
    wait_for_service $REDIS_HOST ${REDIS_PORT:-6379} "Redis"
    
    # 等待服务注册中心
    wait_for_service $SERVICE_REGISTRY_HOST ${SERVICE_REGISTRY_PORT:-3010} "服务注册中心"
    
    log_success "所有依赖服务已就绪"
}

# 健康检查
health_check() {
    local max_attempts=10
    local attempt=1
    
    log_info "执行健康检查..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:${PORT}/health > /dev/null 2>&1; then
            log_success "健康检查通过"
            return 0
        fi
        
        echo -n "."
        sleep 3
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 优雅关闭处理
graceful_shutdown() {
    log_info "接收到关闭信号，正在优雅关闭..."
    
    # 发送SIGTERM信号给Node.js进程
    if [ ! -z "$NODE_PID" ]; then
        kill -TERM $NODE_PID
        wait $NODE_PID
    fi
    
    log_success "服务已优雅关闭"
    exit 0
}

# 设置信号处理
trap graceful_shutdown SIGTERM SIGINT

# 主函数
main() {
    log_info "启动DL引擎协作服务..."
    log_info "实例ID: ${INSTANCE_ID:-collaboration-default}"
    log_info "端口: ${PORT}"
    log_info "环境: ${NODE_ENV}"
    
    # 检查环境变量
    check_env
    
    # 等待依赖服务
    wait_for_dependencies
    
    # 启动Node.js应用
    log_info "启动Node.js应用..."
    node dist/main.js &
    NODE_PID=$!
    
    # 等待应用启动
    sleep 5
    
    # 健康检查
    if health_check; then
        log_success "协作服务启动成功！"
        log_info "PID: $NODE_PID"
        log_info "健康检查: http://localhost:${PORT}/health"
        log_info "API文档: http://localhost:${PORT}/api/docs"
    else
        log_error "协作服务启动失败"
        kill $NODE_PID 2>/dev/null || true
        exit 1
    fi
    
    # 等待进程结束
    wait $NODE_PID
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  --help, -h          显示帮助信息"
    echo "  --check-deps        仅检查依赖服务"
    echo "  --health-check      仅执行健康检查"
    exit 0
elif [ "$1" = "--check-deps" ]; then
    check_env
    wait_for_dependencies
    exit 0
elif [ "$1" = "--health-check" ]; then
    health_check
    exit $?
else
    main
fi
