#!/bin/bash

# ================================
# DL引擎服务注册中心构建修复脚本
# ================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查是否有shared模块的导入
    if grep -r "../../../../shared" src/ 2>/dev/null; then
        log_error "发现外部shared模块依赖，已修复为本地实现"
        return 1
    fi
    
    log_success "依赖检查通过"
}

# 本地构建测试
local_build() {
    log_info "执行本地构建测试..."
    
    # 安装依赖
    npm install --legacy-peer-deps
    
    # 构建项目
    npm run build
    
    log_success "本地构建成功"
}

# Docker构建
docker_build() {
    local tag=${1:-service-registry:latest}
    
    log_info "构建Docker镜像: $tag"
    
    # 构建镜像
    docker build -t $tag .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功: $tag"
    else
        log_error "Docker镜像构建失败"
        return 1
    fi
}

# 测试镜像
test_image() {
    local tag=${1:-service-registry:latest}
    
    log_info "测试Docker镜像..."
    
    # 运行容器测试
    docker run --rm -d \
        --name service-registry-test \
        -p 3010:3010 \
        -p 4010:4010 \
        -e NODE_ENV=production \
        -e DB_HOST=localhost \
        -e REDIS_HOST=localhost \
        $tag
    
    # 等待启动
    sleep 10
    
    # 健康检查
    if curl -f -s http://localhost:4010/health > /dev/null; then
        log_success "镜像测试成功"
        docker stop service-registry-test
        return 0
    else
        log_error "镜像测试失败"
        docker logs service-registry-test
        docker stop service-registry-test
        return 1
    fi
}

# 清理
cleanup() {
    log_info "清理临时文件..."
    rm -rf node_modules dist
    log_success "清理完成"
}

# 主函数
main() {
    echo "=========================================="
    echo "    DL引擎服务注册中心构建修复脚本"
    echo "=========================================="
    
    case "$1" in
        "check")
            check_dependencies
            ;;
        "clean")
            cleanup
            ;;
        "local")
            local_build
            ;;
        "docker")
            docker_build "${2:-service-registry:latest}"
            ;;
        "test")
            test_image "${2:-service-registry:latest}"
            ;;
        "all")
            cleanup
            check_dependencies
            local_build
            docker_build "service-registry:latest"
            test_image "service-registry:latest"
            ;;
        *)
            echo "用法: $0 {check|clean|local|docker|test|all}"
            echo ""
            echo "命令说明:"
            echo "  check   - 检查依赖问题"
            echo "  clean   - 清理临时文件"
            echo "  local   - 本地构建测试"
            echo "  docker  - Docker构建 [tag]"
            echo "  test    - 测试Docker镜像 [tag]"
            echo "  all     - 执行完整构建流程"
            echo ""
            echo "示例:"
            echo "  $0 docker service-registry:v1.0"
            echo "  $0 all"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
