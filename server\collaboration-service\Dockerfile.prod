# ================================
# DL引擎协作服务生产环境 Dockerfile
# 针对生产环境优化的精简版本
# ================================

FROM node:18-alpine AS production

# 安装运行时依赖
RUN apk add --no-cache \
    wget \
    curl \
    dumb-init \
    tzdata \
    tini

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制预构建的应用（假设在CI/CD中已构建）
COPY --chown=nodejs:nodejs dist ./dist
COPY --chown=nodejs:nodejs node_modules ./node_modules
COPY --chown=nodejs:nodejs package*.json ./

# 创建必要目录
RUN mkdir -p /app/logs && chown -R nodejs:nodejs /app

# 环境变量
ENV NODE_ENV=production
ENV PORT=3005
ENV NODE_OPTIONS="--max-old-space-size=512"

# 切换到非root用户
USER nodejs

# 健康检查
HEALTHCHECK --interval=10s --timeout=5s --start-period=15s --retries=3 \
  CMD wget -qO- http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 使用tini作为init进程
ENTRYPOINT ["tini", "--"]

# 启动应用
CMD ["node", "dist/main.js"]
