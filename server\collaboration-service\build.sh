#!/bin/bash

# ================================
# DL引擎协作服务构建脚本
# 解决Docker构建问题
# ================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f package-lock.json
    rm -rf node_modules
    rm -rf dist
}

# 修复依赖
fix_dependencies() {
    log_info "修复package.json依赖版本..."
    
    # 备份原始文件
    cp package.json package.json.backup
    
    # 删除可能有问题的lock文件
    rm -f package-lock.json
    
    log_success "依赖修复完成"
}

# 本地构建测试
local_build() {
    log_info "执行本地构建测试..."
    
    # 安装依赖
    npm install --legacy-peer-deps --force
    
    # 构建项目
    npm run build
    
    log_success "本地构建成功"
}

# Docker构建
docker_build() {
    local dockerfile=${1:-Dockerfile}
    local tag=${2:-dl-engine-collaboration:latest}
    
    log_info "使用 $dockerfile 构建Docker镜像..."
    
    # 构建镜像
    docker build -f $dockerfile -t $tag .
    
    if [ $? -eq 0 ]; then
        log_success "Docker镜像构建成功: $tag"
    else
        log_error "Docker镜像构建失败"
        return 1
    fi
}

# 测试镜像
test_image() {
    local tag=${1:-dl-engine-collaboration:latest}
    
    log_info "测试Docker镜像..."
    
    # 运行容器测试
    docker run --rm -d \
        --name collaboration-test \
        -p 3005:3005 \
        -e NODE_ENV=production \
        -e PORT=3005 \
        -e REDIS_HOST=localhost \
        -e SERVICE_REGISTRY_HOST=localhost \
        $tag
    
    # 等待启动
    sleep 10
    
    # 健康检查
    if curl -f -s http://localhost:3005/health > /dev/null; then
        log_success "镜像测试成功"
        docker stop collaboration-test
        return 0
    else
        log_error "镜像测试失败"
        docker logs collaboration-test
        docker stop collaboration-test
        return 1
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "    DL引擎协作服务构建脚本"
    echo "=========================================="
    
    case "$1" in
        "clean")
            cleanup
            ;;
        "fix")
            fix_dependencies
            ;;
        "local")
            fix_dependencies
            local_build
            ;;
        "docker")
            fix_dependencies
            docker_build "${2:-Dockerfile}" "${3:-dl-engine-collaboration:latest}"
            ;;
        "simple")
            fix_dependencies
            docker_build "Dockerfile.simple" "dl-engine-collaboration:simple"
            ;;
        "test")
            test_image "${2:-dl-engine-collaboration:latest}"
            ;;
        "all")
            cleanup
            fix_dependencies
            local_build
            docker_build "Dockerfile.simple" "dl-engine-collaboration:latest"
            test_image "dl-engine-collaboration:latest"
            ;;
        *)
            echo "用法: $0 {clean|fix|local|docker|simple|test|all}"
            echo ""
            echo "命令说明:"
            echo "  clean   - 清理临时文件"
            echo "  fix     - 修复依赖问题"
            echo "  local   - 本地构建测试"
            echo "  docker  - Docker构建 [dockerfile] [tag]"
            echo "  simple  - 使用简化Dockerfile构建"
            echo "  test    - 测试Docker镜像 [tag]"
            echo "  all     - 执行完整构建流程"
            echo ""
            echo "示例:"
            echo "  $0 simple                    # 使用简化版构建"
            echo "  $0 docker Dockerfile.simple # 指定Dockerfile构建"
            echo "  $0 all                       # 完整流程"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
