# ================================
# DL引擎协作服务 Dockerfile
# 支持实时协作、WebSocket通信、消息压缩
# ================================

# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 复制package文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci --legacy-peer-deps

# 复制源代码
COPY . .

# 构建TypeScript应用
RUN npm run build

# 清理开发依赖，只保留生产依赖
RUN npm prune --production

# ================================
# 生产环境阶段
# ================================
FROM node:18-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 安装运行时依赖
RUN apk add --no-cache \
    wget \
    curl \
    dumb-init \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY --from=builder --chown=nodejs:nodejs /app/package*.json ./

# 复制生产依赖
COPY --from=builder --chown=nodejs:nodejs /app/node_modules ./node_modules

# 复制构建产物
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist

# 创建日志目录
RUN mkdir -p /app/logs && chown -R nodejs:nodejs /app/logs

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3005
ENV INSTANCE_ID=collaboration-default

# 协作服务特定配置
ENV ENABLE_COMPRESSION=true
ENV COMPRESSION_LEVEL=6
ENV MAX_BATCH_SIZE=50
ENV MAX_BATCH_WAIT_TIME=50

# WebSocket配置
ENV WS_HEARTBEAT_INTERVAL=30000
ENV WS_MAX_CONNECTIONS=1000
ENV WS_MESSAGE_SIZE_LIMIT=1048576

# Redis配置
ENV REDIS_HOST=redis
ENV REDIS_PORT=6379
ENV REDIS_DB=0

# 服务注册配置
ENV SERVICE_REGISTRY_HOST=service-registry
ENV SERVICE_REGISTRY_PORT=3010

# 监控配置
ENV METRICS_ENABLED=true
ENV LOG_LEVEL=info

# 安全配置
ENV NODE_OPTIONS="--max-old-space-size=512"

# 切换到非root用户
USER nodejs

# 健康检查
HEALTHCHECK --interval=10s --timeout=5s --start-period=15s --retries=3 \
  CMD wget -qO- http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 使用dumb-init作为PID 1，处理信号
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "dist/main.js"]
