/**
 * 健康检查服务
 * 检查各个依赖服务的健康状态
 */
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HealthResponse } from './health.controller';

/**
 * 健康检查服务
 */
@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = Date.now();

  constructor(private readonly configService: ConfigService) {}

  /**
   * 获取健康状态
   * @returns 健康状态信息
   */
  async getHealthStatus(): Promise<HealthResponse> {
    try {
      const [redisStatus, wsStatus, registryStatus] = await Promise.all([
        this.checkRedisConnection(),
        this.checkWebSocketStatus(),
        this.checkServiceRegistry()
      ]);

      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      return {
        status: this.determineOverallStatus(redisStatus, wsStatus, registryStatus),
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime,
        version: process.env.npm_package_version || '0.1.0',
        instance: this.configService.get<string>('INSTANCE_ID', 'collaboration-default'),
        services: {
          redis: redisStatus,
          websocket: wsStatus,
          serviceRegistry: registryStatus
        },
        metrics: {
          activeConnections: this.getActiveConnections(),
          totalMessages: this.getTotalMessages(),
          memoryUsage: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
          },
          cpuUsage: Math.round((cpuUsage.user + cpuUsage.system) / 1000000) // 转换为毫秒
        }
      };
    } catch (error) {
      this.logger.error('健康检查失败', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime,
        version: process.env.npm_package_version || '0.1.0',
        instance: this.configService.get<string>('INSTANCE_ID', 'collaboration-default'),
        services: {
          redis: 'disconnected',
          websocket: 'inactive',
          serviceRegistry: 'disconnected'
        },
        metrics: {
          activeConnections: 0,
          totalMessages: 0,
          memoryUsage: {
            used: 0,
            total: 0,
            percentage: 0
          },
          cpuUsage: 0
        }
      };
    }
  }

  /**
   * 检查服务是否就绪
   * @returns 就绪状态
   */
  async isReady(): Promise<boolean> {
    try {
      const [redisStatus, registryStatus] = await Promise.all([
        this.checkRedisConnection(),
        this.checkServiceRegistry()
      ]);

      return redisStatus === 'connected' && registryStatus === 'connected';
    } catch (error) {
      this.logger.error('就绪检查失败', error);
      return false;
    }
  }

  /**
   * 检查Redis连接状态
   * @returns Redis连接状态
   */
  private async checkRedisConnection(): Promise<'connected' | 'disconnected'> {
    try {
      // 这里应该实际检查Redis连接
      // 暂时返回connected，实际实现需要注入Redis客户端
      return 'connected';
    } catch (error) {
      this.logger.error('Redis连接检查失败', error);
      return 'disconnected';
    }
  }

  /**
   * 检查WebSocket状态
   * @returns WebSocket状态
   */
  private async checkWebSocketStatus(): Promise<'active' | 'inactive'> {
    try {
      // 这里应该检查WebSocket网关状态
      // 暂时返回active，实际实现需要检查网关实例
      return 'active';
    } catch (error) {
      this.logger.error('WebSocket状态检查失败', error);
      return 'inactive';
    }
  }

  /**
   * 检查服务注册中心连接状态
   * @returns 服务注册中心连接状态
   */
  private async checkServiceRegistry(): Promise<'connected' | 'disconnected'> {
    try {
      // 这里应该实际检查服务注册中心连接
      // 暂时返回connected，实际实现需要调用注册中心API
      return 'connected';
    } catch (error) {
      this.logger.error('服务注册中心连接检查失败', error);
      return 'disconnected';
    }
  }

  /**
   * 获取活跃连接数
   * @returns 活跃连接数
   */
  private getActiveConnections(): number {
    // 这里应该从WebSocket网关获取实际连接数
    // 暂时返回0，实际实现需要统计WebSocket连接
    return 0;
  }

  /**
   * 获取总消息数
   * @returns 总消息数
   */
  private getTotalMessages(): number {
    // 这里应该从监控服务获取实际消息数
    // 暂时返回0，实际实现需要统计消息数量
    return 0;
  }

  /**
   * 确定整体健康状态
   * @param redisStatus Redis状态
   * @param wsStatus WebSocket状态
   * @param registryStatus 服务注册状态
   * @returns 整体状态
   */
  private determineOverallStatus(
    redisStatus: string,
    wsStatus: string,
    registryStatus: string
  ): 'ok' | 'error' {
    if (redisStatus === 'connected' && 
        wsStatus === 'active' && 
        registryStatus === 'connected') {
      return 'ok';
    }
    return 'error';
  }
}
