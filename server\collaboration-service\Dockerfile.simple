# ================================
# DL引擎协作服务简化版 Dockerfile
# 解决依赖冲突问题的临时方案
# ================================

FROM node:18-alpine

# 安装必要的系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    wget \
    curl \
    dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 设置工作目录
WORKDIR /app

# 复制package文件和npm配置
COPY package*.json .npmrc ./

# 设置npm配置
RUN npm config set legacy-peer-deps true && \
    npm config set audit false && \
    npm config set fund false

# 安装依赖（使用--force强制解决冲突）
RUN npm install --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 清理不必要的文件
RUN rm -rf src tests *.md && \
    npm cache clean --force

# 创建日志目录
RUN mkdir -p /app/logs && chown -R nodejs:nodejs /app

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3005
ENV INSTANCE_ID=collaboration-default

# 协作服务配置
ENV ENABLE_COMPRESSION=true
ENV COMPRESSION_LEVEL=6
ENV MAX_BATCH_SIZE=50
ENV MAX_BATCH_WAIT_TIME=50

# WebSocket配置
ENV WS_HEARTBEAT_INTERVAL=30000
ENV WS_MAX_CONNECTIONS=1000
ENV WS_MESSAGE_SIZE_LIMIT=1048576

# Redis配置
ENV REDIS_HOST=redis
ENV REDIS_PORT=6379

# 服务注册配置
ENV SERVICE_REGISTRY_HOST=service-registry
ENV SERVICE_REGISTRY_PORT=3010

# 性能配置
ENV NODE_OPTIONS="--max-old-space-size=512"

# 切换到非root用户
USER nodejs

# 健康检查
HEALTHCHECK --interval=10s --timeout=5s --start-period=15s --retries=3 \
  CMD wget -qO- http://localhost:$PORT/health || exit 1

# 暴露端口
EXPOSE $PORT

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]
