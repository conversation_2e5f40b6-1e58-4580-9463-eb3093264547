{"name": "collaboration-service", "version": "0.1.0", "description": "DL（Digital Learning）引擎协作服务", "author": "IR Engine Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^9.4.3", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.4.3", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.0.3", "@nestjs/microservices": "^9.4.3", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-ws": "^9.4.3", "@nestjs/swagger": "^6.3.0", "@nestjs/websockets": "^9.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "uuid": "^9.0.0", "ws": "^8.13.0"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.13", "@types/jest": "29.5.0", "@types/node": "18.15.11", "@types/supertest": "^2.0.11", "@types/uuid": "^9.0.1", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.5.0", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.5", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.2.0", "typescript": "^4.7.4", "webpack": "^5.99.9"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}