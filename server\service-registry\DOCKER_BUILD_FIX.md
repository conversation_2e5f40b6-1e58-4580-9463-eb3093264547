# Service Registry Docker构建错误修复指南

## 问题分析

您遇到的构建错误是因为service-registry服务试图导入外部shared模块：

```
Cannot find module '../../../../shared/event-bus'
```

### 错误原因

1. **路径依赖问题**: service-registry试图导入`../../../../shared/event-bus`
2. **Docker构建上下文**: 只复制了service-registry目录，没有包含shared目录
3. **模块解析失败**: TypeScript无法找到外部shared模块

## 修复方案

### 方案一：使用本地Event Bus实现（推荐）

我已经在service-registry中创建了本地的event-bus实现：

```
src/shared/event-bus/
├── index.ts
├── event-bus.service.ts
├── event-bus.module.ts
├── enhanced-event-bus.service.ts
└── events/
    └── index.ts
```

**修复步骤**：
1. 所有导入路径已更新为本地路径
2. 创建了完整的本地event-bus实现
3. 更新了模块导入配置

### 方案二：从server目录构建（备选）

如果需要使用原始shared模块：

```bash
# 从server目录构建
cd F:\newsystem\server
docker build -f service-registry/Dockerfile.with-shared -t service-registry .
```

## 立即可用的解决方案

### 快速修复

```bash
cd F:\newsystem\server\service-registry

# 使用修复后的Dockerfile构建
docker build -t service-registry .
```

### 使用构建脚本

```bash
# 赋予执行权限
chmod +x build-fix.sh

# 执行完整构建流程
./build-fix.sh all

# 或仅构建Docker镜像
./build-fix.sh docker
```

## 已修复的文件

### 1. 导入路径修复

**修复前**:
```typescript
import { EventBusService } from '../../../../shared/event-bus';
```

**修复后**:
```typescript
import { EventBusService } from '../../shared/event-bus';
```

### 2. 本地Event Bus实现

创建了完整的本地实现：
- `EventBusService` - 基础事件总线服务
- `EnhancedEventBusService` - 增强型事件总线
- `EventBusModule` - 事件总线模块
- 事件常量定义

### 3. 模块配置更新

- `app.module.ts` - 添加EventBusModule
- `registry.module.ts` - 导入EventBusModule

## 验证构建成功

### 检查镜像

```bash
docker images | grep service-registry
```

### 测试运行

```bash
docker run --rm -d \
  --name service-registry-test \
  -p 3010:3010 \
  -p 4010:4010 \
  -e NODE_ENV=production \
  -e DB_HOST=mysql \
  -e REDIS_HOST=redis \
  service-registry

# 检查健康状态
curl http://localhost:4010/health

# 停止测试容器
docker stop service-registry-test
```

## 构建选项

### 标准构建

```bash
docker build -t service-registry .
```

### 带shared模块的构建

```bash
# 从server目录执行
cd F:\newsystem\server
docker build -f service-registry/Dockerfile.with-shared -t service-registry .
```

### 使用构建脚本

```bash
./build-fix.sh docker service-registry:v1.0
```

## 故障排除

### 如果仍有导入错误

1. **检查导入路径**:
```bash
grep -r "../../../../shared" src/
```

2. **清理并重新构建**:
```bash
rm -rf node_modules dist
npm install
npm run build
```

3. **检查模块导入**:
确保所有使用EventBusService的模块都导入了EventBusModule

### 如果Docker构建失败

1. **清理Docker缓存**:
```bash
docker system prune -f
```

2. **使用无缓存构建**:
```bash
docker build --no-cache -t service-registry .
```

3. **检查构建日志**:
```bash
docker build -t service-registry . 2>&1 | tee build.log
```

## 预防措施

### 1. 依赖管理

- 避免跨目录的相对路径导入
- 使用npm包或monorepo工具管理共享代码
- 在package.json中明确声明依赖

### 2. Docker最佳实践

- 确保构建上下文包含所有必需文件
- 使用.dockerignore优化构建性能
- 分层构建以利用缓存

### 3. 模块设计

- 每个服务应该是自包含的
- 共享代码应该作为独立包发布
- 避免深层目录依赖

## 成功标志

构建成功后，您应该看到：

1. **Docker镜像创建成功**
2. **容器可以正常启动**
3. **健康检查端点响应正常**
4. **服务注册功能正常工作**

现在您可以使用修复后的配置成功构建service-registry的Docker镜像了！
