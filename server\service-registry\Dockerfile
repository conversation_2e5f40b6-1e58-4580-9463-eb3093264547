# ================================
# DL引擎服务注册中心 Dockerfile
# 使用本地event-bus实现
# ================================

# 构建阶段
FROM node:18-alpine AS builder

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --legacy-peer-deps

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# ================================
# 生产环境阶段
# ================================
FROM node:18-alpine AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 安装运行时依赖
RUN apk add --no-cache \
    wget \
    curl \
    dumb-init \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY --from=builder --chown=nodejs:nodejs /app/package*.json ./

# 安装生产依赖
RUN npm ci --legacy-peer-deps --only=production

# 复制构建产物
COPY --from=builder --chown=nodejs:nodejs /app/dist ./dist

# 创建日志目录
RUN mkdir -p /app/logs && chown -R nodejs:nodejs /app/logs

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3010
ENV HEALTH_PORT=4010

# 数据库配置
ENV DB_HOST=mysql
ENV DB_PORT=3306
ENV DB_USERNAME=root
ENV DB_PASSWORD=dl_engine_password_2024
ENV DB_DATABASE=dl_engine

# Redis配置
ENV REDIS_HOST=redis
ENV REDIS_PORT=6379
ENV REDIS_DB=0

# JWT配置
ENV JWT_SECRET=dl_engine_jwt_secret_key_2024_very_secure

# 监控配置
ENV METRICS_ENABLED=true
ENV LOG_LEVEL=info

# 安全配置
ENV NODE_OPTIONS="--max-old-space-size=512"

# 切换到非root用户
USER nodejs

# 健康检查
HEALTHCHECK --interval=10s --timeout=5s --start-period=15s --retries=3 \
  CMD wget -qO- http://localhost:$HEALTH_PORT/health || exit 1

# 暴露端口
EXPOSE $PORT $HEALTH_PORT

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "dist/main.js"]
